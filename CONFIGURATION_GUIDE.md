# Society Configuration Guide

This guide explains how to configure the residential society website for different types of communities: apartments, independent houses, or mixed developments.

## Quick Setup

### Step 1: Choose Your Society Type

Open `src/config/societyConfig.js` and modify the `type` field:

```javascript
export const SOCIETY_CONFIG = {
  // Change this to match your society type
  type: 'apartments',  // Options: 'apartments', 'houses', or 'mixed'
  
  // Your society name
  name: 'Your Society Name Here',
  
  // Other basic info
  establishedYear: 1995,
  totalFamilies: 300,
  // ... rest of config
}
```

### Step 2: Update Society Information

Modify the basic information fields:
- `name`: Your society's name
- `establishedYear`: When your society was established
- `totalFamilies`: Total number of families/households

## Society Types Explained

### 🏢 Apartments (`type: 'apartments'`)

**Best for:** High-rise buildings, apartment complexes, condominiums

**Features:**
- Block-based layout (Block A, Block B, etc.)
- Apartment units (1BHK, 2BHK, 3BHK, etc.)
- Shared facilities like elevators, common parking
- Building maintenance responsibilities

**Example Layout:**
```javascript
units: [
  { name: 'Block A', count: 75, types: '2BHK & 3BHK' },
  { name: 'Block B', count: 80, types: '1BHK & 2BHK' },
  // ...
]
```

### 🏠 Houses (`type: 'houses'`)

**Best for:** Independent houses, villas, row houses, gated communities

**Features:**
- Sector-based layout (Sector A, Sector B, etc.)
- Independent houses with plot sizes
- Individual parking spaces
- Street and infrastructure maintenance

**Example Layout:**
```javascript
units: [
  { name: 'Sector A', count: 45, types: 'Villa plots (200-300 sq yards)' },
  { name: 'Sector B', count: 55, types: 'Independent houses (150-250 sq yards)' },
  // ...
]
```

### 🏘️ Mixed (`type: 'mixed'`)

**Best for:** Communities with both apartments and independent houses

**Features:**
- Combined layout with both blocks and sectors
- Mixed unit types (apartments + houses)
- Comprehensive facilities for both housing types
- Diverse maintenance responsibilities

**Example Layout:**
```javascript
units: [
  { name: 'Apartment Block A', count: 60, types: '2BHK & 3BHK apartments' },
  { name: 'Villa Sector C', count: 30, types: 'Independent villas' },
  // ...
]
```

## Customization Options

### 1. Layout Configuration

For each society type, you can customize:

```javascript
layouts: {
  apartments: {
    title: 'Residential Blocks',  // Section title
    icon: '🏢',                   // Display icon
    units: [                      // Your blocks/sectors
      { 
        name: 'Block A',          // Block/Sector name
        count: 75,                // Number of units
        types: '2BHK & 3BHK'      // Unit types description
      },
      // Add more blocks as needed
    ],
    parkingInfo: 'Covered parking for 400+ vehicles'  // Parking description
  }
}
```

### 2. Common Facilities

Customize facilities based on your society type:

```javascript
commonFacilities: {
  apartments: [
    'Central Park with jogging track',
    'Swimming pool and gym',
    'Elevator maintenance',
    // Add your facilities
  ]
}
```

### 3. RWA Responsibilities

Tailor RWA responsibilities to your society type:

```javascript
rwaResponsibilities: {
  apartments: [
    'Building and elevator maintenance',
    'Security management',
    // Add specific responsibilities
  ]
}
```

### 4. History Content

Customize the history section:

```javascript
history: {
  apartments: {
    description: 'Your society establishment story...',
    growth: 'Your society growth and development story...'
  }
}
```

## Common Customizations

### Adding New Blocks/Sectors

To add more residential units:

```javascript
units: [
  // Existing units...
  { name: 'Block E', count: 45, types: '4BHK Penthouses' },
  { name: 'Sector F', count: 20, types: 'Premium villas' }
]
```

### Adding New Facilities

To add more common facilities:

```javascript
commonFacilities: {
  apartments: [
    // Existing facilities...
    'Rooftop garden',
    'Business center',
    'Kids play area',
    'Senior citizen corner'
  ]
}
```

### Updating RWA Team

The RWA executive committee is defined in the About.jsx component. To update:

1. Open `src/pages/About.jsx`
2. Find the "Current Executive Committee" section
3. Update the names and positions

## Examples for Different Society Types

### Example 1: Luxury Apartment Complex

```javascript
export const SOCIETY_CONFIG = {
  type: 'apartments',
  name: 'Skyline Towers',
  establishedYear: 2010,
  totalFamilies: 450,
  // ... customize layouts, facilities for luxury apartments
}
```

### Example 2: Independent Houses Community

```javascript
export const SOCIETY_CONFIG = {
  type: 'houses',
  name: 'Green Valley Homes',
  establishedYear: 2005,
  totalFamilies: 180,
  // ... customize layouts, facilities for independent houses
}
```

### Example 3: Mixed Development

```javascript
export const SOCIETY_CONFIG = {
  type: 'mixed',
  name: 'Harmony Residences',
  establishedYear: 2008,
  totalFamilies: 320,
  // ... customize layouts, facilities for mixed development
}
```

## Testing Your Configuration

After making changes:

1. Save the `societyConfig.js` file
2. Restart your development server (`npm run dev`)
3. Check the About page to see your changes
4. Verify the Home page reflects the new society type
5. Test all pages to ensure consistency

## Troubleshooting

### Common Issues:

1. **Page not updating:** Restart the development server
2. **Missing data:** Check for typos in the configuration object
3. **Layout issues:** Ensure all required fields are present in the config

### Getting Help:

If you need assistance with configuration:
1. Check the console for any JavaScript errors
2. Verify the configuration object structure matches the examples
3. Ensure all array items have the required fields (`name`, `count`, `types`)

## Advanced Customization

For more advanced customizations beyond the configuration file:

1. **Custom Icons:** Update the `icon` fields in the configuration
2. **Additional Sections:** Modify the About.jsx component directly
3. **Styling Changes:** Update the CSS files for visual customization
4. **New Features:** Add new sections to the configuration and update components accordingly

Remember to backup your configuration before making major changes!
