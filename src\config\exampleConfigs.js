// Example configurations for different society types
// Copy and paste these into societyConfig.js to see different layouts

export const APARTMENT_EXAMPLE = {
  type: 'apartments',
  name: 'Skyline Towers',
  establishedYear: 2010,
  totalFamilies: 450,
  
  layouts: {
    apartments: {
      title: 'Residential Towers',
      icon: '🏢',
      units: [
        { name: 'Tower A', count: 120, types: '2BHK & 3BHK' },
        { name: 'Tower B', count: 110, types: '1BHK & 2BHK' },
        { name: 'Tower C', count: 130, types: '3BHK & 4BHK' },
        { name: 'Tower D', count: 90, types: 'Penthouses' }
      ],
      parkingInfo: 'Multi-level covered parking for 600+ vehicles'
    }
  },
  
  commonFacilities: {
    apartments: [
      'Rooftop infinity pool with city views',
      'State-of-the-art fitness center',
      'Children\'s play area and daycare',
      'Multi-purpose community hall',
      '24/7 concierge and security',
      'High-speed elevators',
      'Power backup and water storage',
      'Landscaped gardens and jogging track'
    ]
  },
  
  rwaResponsibilities: {
    apartments: [
      'Building and elevator maintenance',
      'Common area upkeep and cleaning',
      'Security and access control',
      'Organizing resident events',
      'Utility management and billing',
      'Vendor coordination',
      'Compliance with building regulations'
    ]
  },
  
  history: {
    apartments: {
      description: 'Established in 2010, Skyline Towers has redefined luxury apartment living in the city. Starting with a vision to create premium residential spaces, we have grown to become one of the most sought-after apartment complexes.',
      growth: 'Over the years, we have continuously upgraded our facilities, added smart home features, and maintained the highest standards of living that make Skyline Towers the preferred choice for urban professionals and families.'
    }
  }
}

export const HOUSES_EXAMPLE = {
  type: 'houses',
  name: 'Green Valley Estates',
  establishedYear: 2005,
  totalFamilies: 180,
  
  layouts: {
    houses: {
      title: 'Residential Sectors',
      icon: '🏠',
      units: [
        { name: 'Villa Sector A', count: 45, types: 'Premium villas (400-500 sq yards)' },
        { name: 'Garden Homes B', count: 55, types: 'Independent houses (250-350 sq yards)' },
        { name: 'Row Houses C', count: 50, types: 'Duplex row houses (200-300 sq yards)' },
        { name: 'Executive Plots D', count: 30, types: 'Luxury villas (500+ sq yards)' }
      ],
      parkingInfo: 'Private driveways and garages for each house'
    }
  },
  
  commonFacilities: {
    houses: [
      'Central clubhouse with banquet hall',
      'Olympic-size swimming pool',
      'Tennis and badminton courts',
      'Children\'s adventure playground',
      'Jogging track around the perimeter',
      '24/7 gated security with CCTV',
      'Landscaped parks and gardens',
      'Underground utilities and street lighting'
    ]
  },
  
  rwaResponsibilities: {
    houses: [
      'Common area and park maintenance',
      'Street cleaning and garbage collection',
      'Security gate and patrol management',
      'Organizing community festivals',
      'Infrastructure and utility coordination',
      'Dispute resolution between neighbors',
      'Liaison with municipal authorities'
    ]
  },
  
  history: {
    houses: {
      description: 'Established in 2005, Green Valley Estates was conceived as a premium gated community for discerning families seeking independent house living. Starting with 50 beautiful homes, we have grown into a prestigious neighborhood.',
      growth: 'Over the years, we have developed into a self-contained community with world-class amenities, tree-lined streets, and a strong sense of neighborhood that makes Green Valley Estates a coveted address.'
    }
  }
}

export const MIXED_EXAMPLE = {
  type: 'mixed',
  name: 'Harmony Residences',
  establishedYear: 2008,
  totalFamilies: 320,
  
  layouts: {
    mixed: {
      title: 'Residential Complex',
      icon: '🏘️',
      units: [
        { name: 'Apartment Block A', count: 80, types: '2BHK & 3BHK apartments' },
        { name: 'Apartment Block B', count: 70, types: '1BHK & 2BHK apartments' },
        { name: 'Villa Sector C', count: 40, types: 'Independent villas (300-400 sq yards)' },
        { name: 'Row Houses D', count: 35, types: 'Duplex row houses (250-350 sq yards)' },
        { name: 'Penthouse Tower E', count: 15, types: 'Luxury penthouses' }
      ],
      parkingInfo: 'Covered parking for apartments, private parking for houses'
    }
  },
  
  commonFacilities: {
    mixed: [
      'Central clubhouse and community center',
      'Swimming pool complex with kids pool',
      'Multi-sport courts and fitness center',
      'Separate play areas for different age groups',
      'Jogging track connecting all sectors',
      '24/7 security with multiple entry points',
      'Landscaped gardens and water features',
      'Shopping arcade and convenience stores'
    ]
  },
  
  rwaResponsibilities: {
    mixed: [
      'Comprehensive facility management',
      'Building and infrastructure maintenance',
      'Integrated security management',
      'Organizing diverse community events',
      'Coordinating between apartment and house residents',
      'Financial planning for varied maintenance needs',
      'Ensuring harmony between different housing types'
    ]
  },
  
  history: {
    mixed: {
      description: 'Established in 2008, Harmony Residences was designed as an innovative mixed-development community offering both apartment and independent house living options. This unique concept caters to diverse lifestyle preferences within a single community.',
      growth: 'Over the years, we have successfully created a harmonious blend of urban convenience and suburban tranquility, making Harmony Residences a model for integrated community living.'
    }
  }
}

// Instructions for using these examples:
// 1. Choose the example that matches your society type
// 2. Copy the entire configuration object
// 3. Replace the SOCIETY_CONFIG in societyConfig.js with your chosen example
// 4. Customize the details to match your specific society
// 5. Save and restart your development server to see the changes
