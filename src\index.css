:root {
  /* Hero Color Palette */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-gradient-reverse: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-medium: rgba(102, 126, 234, 0.3);
  --primary-dark: #5a6fd8;
  --secondary-dark: #6a4190;

  /* Background Colors */
  --bg-gradient: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  --card-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
  --card-border: 1px solid rgba(102, 126, 234, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: var(--bg-gradient);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  background: var(--primary-gradient);
  color: white;
  text-decoration: none;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px var(--primary-medium);
}

.btn:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: var(--primary-gradient-reverse);
  box-shadow: 0 2px 8px rgba(118, 75, 162, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--primary-dark) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(118, 75, 162, 0.4);
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  margin-bottom: 20px;
  border: var(--card-border);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.grid {
  display: grid;
  gap: 20px;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }
