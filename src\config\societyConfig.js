// Society Configuration
// Change this configuration to match your society type and details

export const SOCIETY_CONFIG = {
  // Society Type: 'apartments' or 'houses' or 'mixed'
  type: 'houses', // Change this to 'houses' or 'mixed' as needed
  
  // Basic Information
  name: 'Indus Valley Residential Society',
  establishedYear: 2018,
  totalFamilies: 200,
  
  // Layout Configuration based on society type
  layouts: {
    apartments: {
      title: 'Residential Blocks',
      icon: '🏢',
      units: [
        { name: 'Block A', count: 75, types: '2BHK & 3BHK' },
        { name: 'Block B', count: 80, types: '1BHK & 2BHK' },
        { name: 'Block C', count: 90, types: '2BHK & 3BHK' },
        { name: 'Block D', count: 65, types: '3BHK & 4BHK' }
      ],
      parkingInfo: 'Covered parking for 400+ vehicles'
    },
    houses: {
      title: 'Residential Plots',
      icon: '🏠',
      units: [
        { name: 'Sector A', count: 45, types: 'Villa plots (200-300 sq yards)' },
        { name: 'Sector B', count: 55, types: 'Independent houses (150-250 sq yards)' },
        { name: 'Sector C', count: 40, types: 'Duplex houses (300-400 sq yards)' },
        { name: 'Sector D', count: 35, types: 'Premium villas (400+ sq yards)' }
      ],
      parkingInfo: 'Individual parking spaces with each house'
    },
    mixed: {
      title: 'Residential Units',
      icon: '🏘️',
      units: [
        { name: 'Apartment Block A', count: 60, types: '2BHK & 3BHK apartments' },
        { name: 'Apartment Block B', count: 50, types: '1BHK & 2BHK apartments' },
        { name: 'Villa Sector C', count: 30, types: 'Independent villas' },
        { name: 'Row Houses D', count: 25, types: 'Duplex row houses' }
      ],
      parkingInfo: 'Covered parking for apartments, individual parking for houses'
    }
  },
  
  // Common Facilities (can be customized based on society type)
  commonFacilities: {
    apartments: [
      'Central Park with jogging track',
      'Children\'s playground',
      'Community hall for events',
      'Swimming pool and gym',
      '24/7 security and CCTV surveillance',
      'Covered parking for 400+ vehicles',
      'Elevator maintenance',
      'Common area cleaning'
    ],
    houses: [
      'Central Park with jogging track',
      'Children\'s playground',
      'Community clubhouse',
      'Swimming pool and sports complex',
      '24/7 security with gated entry',
      'Street lighting and maintenance',
      'Landscaped gardens',
      'Underground utilities'
    ],
    mixed: [
      'Central Park with jogging track',
      'Children\'s playground',
      'Community hall and clubhouse',
      'Swimming pool and gym',
      '24/7 security and CCTV surveillance',
      'Mixed parking facilities',
      'Landscaped common areas',
      'Utility maintenance services'
    ]
  },
  
  // RWA Responsibilities (varies by society type)
  rwaResponsibilities: {
    apartments: [
      'Maintenance and upkeep of common areas',
      'Elevator and building maintenance',
      'Security and safety management',
      'Organizing community events and festivals',
      'Resolving resident grievances',
      'Financial management and budgeting',
      'Liaison with local authorities'
    ],
    houses: [
      'Common area and park maintenance',
      'Street and infrastructure upkeep',
      'Security and gate management',
      'Organizing community events and festivals',
      'Resolving neighbor disputes',
      'Financial management and budgeting',
      'Liaison with municipal authorities'
    ],
    mixed: [
      'Maintenance of all common facilities',
      'Building and infrastructure upkeep',
      'Comprehensive security management',
      'Organizing community events and festivals',
      'Resolving resident grievances',
      'Financial management and budgeting',
      'Liaison with local authorities'
    ]
  },
  
  // History content (can be customized)
  history: {
    apartments: {
      description: 'Established in 1995, our residential society has grown from a small community of 50 families to a thriving neighborhood of over 300 households. Built with a vision of creating a harmonious living environment, the society has consistently maintained high standards of infrastructure and community living.',
      growth: 'Over the years, we have witnessed the growth of beautiful gardens, the establishment of recreational facilities, and the formation of a strong community bond that makes our society a preferred choice for families seeking quality apartment living.'
    },
    houses: {
      description: 'Established in 1995, our residential community began as a planned development of 50 independent houses and has grown into a prestigious neighborhood of over 300 beautiful homes. Built with a vision of creating a harmonious living environment, the society has consistently maintained high standards of infrastructure and community living.',
      growth: 'Over the years, we have witnessed the development of beautiful landscaped streets, the establishment of recreational facilities, and the formation of a strong community bond that makes our society a preferred choice for families seeking independent house living.'
    },
    mixed: {
      description: 'Established in 1995, our residential society has evolved from a small community of 50 families to a diverse neighborhood of over 300 households, featuring both modern apartments and independent houses. Built with a vision of creating a harmonious living environment, the society has consistently maintained high standards of infrastructure and community living.',
      growth: 'Over the years, we have witnessed the growth of beautiful gardens, the establishment of recreational facilities, and the formation of a strong community bond that makes our society a preferred choice for families seeking diverse housing options.'
    }
  }
}

// Helper function to get current society configuration
export const getSocietyConfig = () => {
  const currentType = SOCIETY_CONFIG.type
  return {
    ...SOCIETY_CONFIG,
    currentLayout: SOCIETY_CONFIG.layouts[currentType],
    currentFacilities: SOCIETY_CONFIG.commonFacilities[currentType],
    currentResponsibilities: SOCIETY_CONFIG.rwaResponsibilities[currentType],
    currentHistory: SOCIETY_CONFIG.history[currentType]
  }
}
