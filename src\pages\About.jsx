import React from 'react'
import { getSocietyConfig } from '../config/societyConfig'

const About = () => {
  const config = getSocietyConfig()

  return (
    <div>
      {/* Page Header */}
      <div className="page-header">
        <div className="container">
          <h1>About Our Society</h1>
          <p>Learn about our history, layout, and community</p>
        </div>
      </div>

      <div className="container">
        {/* Society Type Indicator */}
        <section className="card mb-3" style={{ backgroundColor: '#e8f4fd', border: '2px solid #007bff' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
            <span style={{ fontSize: '1.5rem' }}>{config.currentLayout.icon}</span>
            <h3 style={{ margin: 0, color: '#007bff' }}>
              {config.type === 'apartments' ? 'Apartment Complex' :
               config.type === 'houses' ? 'Independent Houses Community' :
               'Mixed Residential Society'}
            </h3>
          </div>
          <p style={{ margin: 0, color: '#0056b3' }}>
            <strong>{config.name}</strong> - A {config.type === 'mixed' ? 'diverse community with both apartments and houses' :
            config.type === 'apartments' ? 'modern apartment complex' : 'premium independent houses community'}
          </p>
        </section>

        {/* Colony History */}
        <section className="card mb-3">
          <h2 className="mb-2">🏛️ Our History</h2>
          <p>
            {config.currentHistory.description}
          </p>
          <p>
            {config.currentHistory.growth}
          </p>
        </section>

        {/* Layout Information */}
        <section className="card mb-3">
          <h2 className="mb-2">🗺️ Society Layout</h2>
          <div className="grid grid-2">
            <div>
              <h4>{config.currentLayout.icon} {config.currentLayout.title}</h4>
              <ul>
                {config.currentLayout.units.map((unit, index) => (
                  <li key={index}>
                    <strong>{unit.name}:</strong> {unit.count} {config.type === 'apartments' ? 'apartments' :
                    config.type === 'houses' ? 'houses' : 'units'} ({unit.types})
                  </li>
                ))}
              </ul>
              <p style={{ marginTop: '15px', fontSize: '0.9rem', color: '#666' }}>
                <strong>Parking:</strong> {config.currentLayout.parkingInfo}
              </p>
            </div>
            <div>
              <h4>🏛️ Common Facilities</h4>
              <ul>
                {config.currentFacilities.map((facility, index) => (
                  <li key={index}>{facility}</li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* RWA Information */}
        <section className="card mb-3">
          <h2 className="mb-2">🤝 Residents Welfare Association (RWA)</h2>
          <p>
            Our RWA is the backbone of our community, working tirelessly to ensure the welfare and 
            well-being of all residents. The association is democratically elected every two years 
            and comprises dedicated volunteers from our community.
          </p>
          
          <div className="grid grid-2 mt-2">
            <div>
              <h4>Current Executive Committee</h4>
              <ul>
                <li><strong>President:</strong> Mr. Rajesh Kumar</li>
                <li><strong>Vice President:</strong> Mrs. Priya Sharma</li>
                <li><strong>Secretary:</strong> Mr. Amit Patel</li>
                <li><strong>Treasurer:</strong> Mrs. Sunita Gupta</li>
                <li><strong>Joint Secretary:</strong> Mr. Vikash Singh</li>
              </ul>
            </div>
            <div>
              <h4>Key Responsibilities</h4>
              <ul>
                {config.currentResponsibilities.map((responsibility, index) => (
                  <li key={index}>{responsibility}</li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Vision & Mission */}
        <section className="card">
          <h2 className="mb-2">🎯 Our Vision & Mission</h2>
          <div className="grid grid-2">
            <div>
              <h4>Vision</h4>
              <p>
                To create and maintain a harmonious, safe, and sustainable living environment 
                that promotes community spirit and enhances the quality of life for all residents.
              </p>
            </div>
            <div>
              <h4>Mission</h4>
              <p>
                To provide excellent maintenance services, foster community engagement, ensure 
                security and safety, and create a platform for residents to connect and thrive together.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}

export default About
