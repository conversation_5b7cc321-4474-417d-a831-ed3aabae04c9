# Residential Society Website

A modern React-based website for residential society management, featuring all essential pages for community communication and management.

## Features

### 🏠 **Home Page**
- Welcome message and basic introduction
- Quick access to important sections
- Latest updates and announcements
- Community highlights

### 📖 **About Us**
- Colony history and development timeline
- Society layout and block information
- RWA (Residents Welfare Association) details
- Vision and mission statements

### 📸 **Gallery**
- Photos of parks, gardens, and green spaces
- Event and festival celebrations
- Community facilities
- Filterable by categories (Parks, Events, Festivals, Facilities)

### 📢 **Notices & Events**
- Latest announcements from RWA
- Upcoming community events
- Priority-based notice system
- Event details with date, time, and location

### 📞 **Resident Directory**
- Password-protected resident contact information
- Search and filter functionality
- Block-wise organization
- Privacy-focused design

### 📧 **Contact**
- RWA office contact information
- Contact form for inquiries
- Emergency contact numbers
- Google Maps integration placeholder
- Office hours and addresses

### 📄 **Downloads**
- Important forms (Maintenance, Visitor Registration, etc.)
- Society rules and regulations
- Maintenance notices and budgets
- Categorized document organization

## Technology Stack

- **Frontend**: React 18 with functional components and hooks
- **Routing**: React Router DOM for navigation
- **Styling**: Custom CSS with responsive design
- **Build Tool**: Vite for fast development and building
- **Configuration**: Flexible society type configuration system

## Prerequisites

Before running this project, make sure you have:

- Node.js (version 16 or higher)
- npm or yarn package manager

## Installation & Setup

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version` and `npm --version`

2. **Configure Your Society Type**:
   - Open `src/config/societyConfig.js`
   - Set `type` to `'apartments'`, `'houses'`, or `'mixed'`
   - Update society name and details
   - See [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) for detailed instructions

3. **Install Dependencies**:
   ```bash
   npm install
   ```

4. **Start Development Server**:
   ```bash
   npm run dev
   ```

5. **Open in Browser**:
   - Navigate to `http://localhost:5173`
   - The application will automatically reload when you make changes

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality

## Project Structure

```
src/
├── components/          # Reusable components
│   ├── Navbar.jsx      # Navigation bar
│   └── Footer.jsx      # Footer component
├── config/             # Configuration files
│   └── societyConfig.js # Society type and details configuration
├── pages/              # Page components
│   ├── Home.jsx        # Home page
│   ├── About.jsx       # About Us page
│   ├── Gallery.jsx     # Photo gallery
│   ├── Notices.jsx     # Notices & Events
│   ├── Directory.jsx   # Resident directory
│   ├── Contact.jsx     # Contact page
│   └── Downloads.jsx   # Downloads page
├── App.jsx             # Main app component
├── App.css             # Application styles
├── main.jsx            # Entry point
└── index.css           # Global styles
```

## Key Features Implementation

### 🏘️ **Flexible Society Configuration**
- Support for apartments, independent houses, or mixed developments
- Easy configuration through `src/config/societyConfig.js`
- Automatic adaptation of content based on society type
- Customizable layouts, facilities, and responsibilities

### 🔒 **Password Protection**
- Resident Directory is protected with a simple password system
- Demo password: `resident123`
- In production, implement proper authentication

### 📱 **Responsive Design**
- Mobile-friendly layout
- Grid system for consistent spacing
- Flexible navigation for different screen sizes

### ⚡ **Interactive Elements**
- Category filtering in Gallery and Downloads
- Tab navigation in Notices & Events
- Search functionality in Resident Directory
- Contact form with validation

## Customization

### 🏗️ **Society Configuration**
1. **Choose Society Type**: Set `type` in `src/config/societyConfig.js` to:
   - `'apartments'` - For apartment complexes and condominiums
   - `'houses'` - For independent houses and villa communities
   - `'mixed'` - For communities with both apartments and houses
2. **Update Basic Info**: Modify society name, establishment year, and family count
3. **Customize Layout**: Add/modify blocks, sectors, and unit types
4. **Configure Facilities**: Update common facilities based on your society type
5. **See [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) for detailed instructions**

### 📝 **Content Customization**
1. **Update Society Information**: Most content auto-updates from configuration
2. **Add Real Images**: Replace placeholder images in Gallery with actual photos
3. **Configure Contact Details**: Update phone numbers, emails, and addresses
4. **Add Real Documents**: Replace download links with actual file URLs

### 🎨 **Styling Customization**
- Modify `src/App.css` for component-specific styles
- Update `src/index.css` for global styles and utilities
- Colors, fonts, and spacing can be easily customized

### 🗺️ **Adding Google Maps**
1. Get a Google Maps API key
2. Install `@googlemaps/react-wrapper`
3. Replace the map placeholder in Contact.jsx

## Security Considerations

- Implement proper authentication for the resident directory
- Use HTTPS in production
- Validate and sanitize all form inputs
- Store sensitive data securely

## Deployment

### Build for Production
```bash
npm run build
```

### Deploy Options
- **Netlify**: Drag and drop the `dist` folder
- **Vercel**: Connect your GitHub repository
- **GitHub Pages**: Use GitHub Actions for deployment
- **Traditional Hosting**: Upload `dist` folder contents

## Future Enhancements

- User authentication system
- Online payment integration
- Real-time notifications
- Mobile app version
- Database integration for dynamic content
- Admin panel for content management

## Support

For technical support or questions about this project:
- Create an issue in the repository
- Contact the development team
- Check the documentation for common solutions

## License

This project is created for residential society use. Modify and distribute as needed for your community.
