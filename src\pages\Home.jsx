import React from 'react'
import { Link } from 'react-router-dom'
import { getSocietyConfig } from '../config/societyConfig'

const Home = () => {
  const config = getSocietyConfig()

  const getHousingDescription = () => {
    switch (config.type) {
      case 'apartments':
        return 'Well-maintained apartments with modern amenities and green spaces for a comfortable lifestyle.'
      case 'houses':
        return 'Beautiful independent houses with private gardens and modern amenities for premium living.'
      case 'mixed':
        return 'Diverse housing options including apartments and independent houses with modern amenities.'
      default:
        return 'Well-maintained residential units with modern amenities and green spaces.'
    }
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <h1>Welcome to {config.name}</h1>
          <p>A peaceful and vibrant community where neighbors become family</p>
          <Link to="/about" className="btn">
            Learn More About Us
          </Link>
        </div>
      </section>

      {/* Quick Info Section */}
      <section className="container mt-3">
        <div className="grid grid-3">
          <div className="card text-center">
            <h3>{config.currentLayout.icon} Modern Living</h3>
            <p>{getHousingDescription()}</p>
          </div>
          <div className="card text-center">
            <h3>🌳 Green Environment</h3>
            <p>Beautiful parks, gardens, and tree-lined pathways creating a serene living environment.</p>
          </div>
          <div className="card text-center">
            <h3>🤝 Strong Community</h3>
            <p>Active residents welfare association fostering community spirit and organizing regular events.</p>
          </div>
        </div>
      </section>

      {/* Latest Updates */}
      <section className="container mt-3">
        <div className="card">
          <h2 className="mb-2">Latest Updates</h2>
          <div className="grid grid-2">
            <div>
              <h4>🎉 Upcoming Festival Celebration</h4>
              <p>Join us for the annual Diwali celebration on October 25th. Cultural programs, food stalls, and fireworks display.</p>
              <Link to="/notices" className="btn btn-secondary mt-1">
                View All Notices
              </Link>
            </div>
            <div>
              <h4>🔧 Maintenance Schedule</h4>
              <p>Monthly maintenance work scheduled for November 1st-3rd. Water supply may be affected during morning hours.</p>
              <Link to="/downloads" className="btn btn-secondary mt-1">
                Download Forms
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="container mt-3">
        <div className="card">
          <h2 className="mb-2 text-center">Quick Access</h2>
          <div className="grid grid-3">
            <Link to="/gallery" className="btn">
              📸 View Gallery
            </Link>
            <Link to="/directory" className="btn">
              📞 Resident Directory
            </Link>
            <Link to="/contact" className="btn">
              📧 Contact RWA
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
